/**
 * Component untuk menangani Human-in-the-Loop (HITL) approval
 * Menampilkan interrupt data dan memungkinkan user untuk approve/reject/modify
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, CheckCircle, XCircle, Edit } from 'lucide-react';

const HITLApproval = ({ 
  interruptData, 
  onApprove, 
  onReject, 
  onModify, 
  isLoading = false 
}) => {
  const [modificationText, setModificationText] = useState('');
  const [showModification, setShowModification] = useState(false);

  if (!interruptData) {
    return null;
  }

  const { type, message, booking_details, payment_details, question } = interruptData;

  const handleModify = () => {
    if (modificationText.trim()) {
      onModify(modificationText);
      setModificationText('');
      setShowModification(false);
    }
  };

  const renderBookingDetails = (details) => {
    if (!details) return null;

    return (
      <div className="space-y-2">
        <h4 className="font-semibold text-sm">Detail Booking:</h4>
        <div className="bg-gray-50 p-3 rounded-md space-y-1">
          {Object.entries(details).map(([key, value]) => (
            <div key={key} className="flex justify-between text-sm">
              <span className="font-medium capitalize">{key.replace('_', ' ')}:</span>
              <span>{value}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderPaymentDetails = (details) => {
    if (!details) return null;

    return (
      <div className="space-y-2">
        <h4 className="font-semibold text-sm">Detail Pembayaran:</h4>
        <div className="bg-blue-50 p-3 rounded-md space-y-1">
          {Object.entries(details).map(([key, value]) => (
            <div key={key} className="flex justify-between text-sm">
              <span className="font-medium capitalize">{key.replace('_', ' ')}:</span>
              <span className={key.includes('amount') || key.includes('total') ? 'font-bold text-blue-600' : ''}>
                {value}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'hotel_booking_approval':
      case 'flight_booking_approval':
      case 'tour_booking_approval':
        return <AlertCircle className="h-5 w-5 text-orange-500" />;
      case 'payment_approval':
        return <AlertCircle className="h-5 w-5 text-blue-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getTypeBadge = (type) => {
    const typeMap = {
      'hotel_booking_approval': { label: 'Hotel Booking', color: 'bg-orange-100 text-orange-800' },
      'flight_booking_approval': { label: 'Flight Booking', color: 'bg-blue-100 text-blue-800' },
      'tour_booking_approval': { label: 'Tour Booking', color: 'bg-green-100 text-green-800' },
      'payment_approval': { label: 'Payment', color: 'bg-purple-100 text-purple-800' },
    };

    const typeInfo = typeMap[type] || { label: 'Approval', color: 'bg-gray-100 text-gray-800' };
    
    return (
      <Badge className={typeInfo.color}>
        {typeInfo.label}
      </Badge>
    );
  };

  return (
    <Card className="w-full max-w-2xl mx-auto border-orange-200 shadow-lg">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getTypeIcon(type)}
            <CardTitle className="text-lg">Konfirmasi Diperlukan</CardTitle>
          </div>
          {getTypeBadge(type)}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Message */}
        {message && (
          <div className="p-3 bg-yellow-50 border-l-4 border-yellow-400 rounded">
            <p className="text-sm font-medium text-yellow-800">{message}</p>
          </div>
        )}

        {/* Question */}
        {question && (
          <div className="p-3 bg-blue-50 border-l-4 border-blue-400 rounded">
            <p className="text-sm font-medium text-blue-800">{question}</p>
          </div>
        )}

        {/* Booking Details */}
        {booking_details && renderBookingDetails(booking_details)}

        {/* Payment Details */}
        {payment_details && renderPaymentDetails(payment_details)}

        {/* Modification Input */}
        {showModification && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Modifikasi yang diinginkan:</label>
            <Textarea
              value={modificationText}
              onChange={(e) => setModificationText(e.target.value)}
              placeholder="Jelaskan perubahan yang Anda inginkan..."
              className="min-h-[80px]"
            />
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2 pt-4 border-t">
          <Button
            onClick={onApprove}
            disabled={isLoading}
            className="flex items-center space-x-2 bg-green-600 hover:bg-green-700"
          >
            <CheckCircle className="h-4 w-4" />
            <span>Setuju</span>
          </Button>

          <Button
            onClick={onReject}
            disabled={isLoading}
            variant="destructive"
            className="flex items-center space-x-2"
          >
            <XCircle className="h-4 w-4" />
            <span>Tolak</span>
          </Button>

          {(type.includes('booking') && !type.includes('payment')) && (
            <>
              {!showModification ? (
                <Button
                  onClick={() => setShowModification(true)}
                  disabled={isLoading}
                  variant="outline"
                  className="flex items-center space-x-2"
                >
                  <Edit className="h-4 w-4" />
                  <span>Modifikasi</span>
                </Button>
              ) : (
                <div className="flex space-x-2">
                  <Button
                    onClick={handleModify}
                    disabled={isLoading || !modificationText.trim()}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Kirim Modifikasi
                  </Button>
                  <Button
                    onClick={() => {
                      setShowModification(false);
                      setModificationText('');
                    }}
                    disabled={isLoading}
                    variant="outline"
                  >
                    Batal
                  </Button>
                </div>
              )}
            </>
          )}
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-sm text-gray-600">Memproses...</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default HITLApproval;
