/**
 * Custom hook untuk menangani Human-in-the-Loop (HITL) operations
 * Menyediakan fungsi untuk start, resume, dan monitor HITL conversations
 */

import { useState, useCallback, useRef } from 'react';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export const useHITL = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentThread, setCurrentThread] = useState(null);
  const [interruptData, setInterruptData] = useState(null);
  const [conversationHistory, setConversationHistory] = useState([]);
  
  // Ref untuk tracking active requests
  const abortControllerRef = useRef(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const startConversation = useCallback(async (message, userContext = null, threadId = null) => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Cancel any ongoing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      
      abortControllerRef.current = new AbortController();
      
      const response = await fetch(`${API_BASE_URL}/api/v1/hitl/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          user_context: userContext,
          thread_id: threadId,
        }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      setCurrentThread(data.thread_id);
      
      if (data.status === 'interrupted') {
        setInterruptData(data.interrupt_data);
      } else {
        setInterruptData(null);
      }
      
      // Update conversation history
      if (data.messages && data.messages.length > 0) {
        setConversationHistory(prev => [...prev, ...data.messages]);
      }
      
      return data;
      
    } catch (err) {
      if (err.name !== 'AbortError') {
        setError(err.message);
        console.error('Error starting HITL conversation:', err);
      }
      throw err;
    } finally {
      setIsLoading(false);
      abortControllerRef.current = null;
    }
  }, []);

  const resumeConversation = useCallback(async (threadId, resumeValue, updateState = null) => {
    try {
      setIsLoading(true);
      setError(null);
      
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      
      abortControllerRef.current = new AbortController();
      
      const response = await fetch(`${API_BASE_URL}/api/v1/hitl/resume`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          thread_id: threadId,
          resume_value: resumeValue,
          update_state: updateState,
        }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.status === 'interrupted') {
        // Still interrupted, update interrupt data
        setInterruptData(data.result?.interrupt_data || null);
      } else {
        // Conversation completed
        setInterruptData(null);
      }
      
      // Update conversation history
      if (data.messages && data.messages.length > 0) {
        setConversationHistory(prev => [...prev, ...data.messages]);
      }
      
      return data;
      
    } catch (err) {
      if (err.name !== 'AbortError') {
        setError(err.message);
        console.error('Error resuming HITL conversation:', err);
      }
      throw err;
    } finally {
      setIsLoading(false);
      abortControllerRef.current = null;
    }
  }, []);

  const getStatus = useCallback(async (threadId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/hitl/status/${threadId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
      
    } catch (err) {
      setError(err.message);
      console.error('Error getting HITL status:', err);
      throw err;
    }
  }, []);

  const deleteThread = useCallback(async (threadId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/hitl/thread/${threadId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Clear current thread if it's the one being deleted
      if (currentThread === threadId) {
        setCurrentThread(null);
        setInterruptData(null);
        setConversationHistory([]);
      }
      
      return true;
      
    } catch (err) {
      setError(err.message);
      console.error('Error deleting HITL thread:', err);
      throw err;
    }
  }, [currentThread]);

  // Helper functions for common HITL operations
  const approveBooking = useCallback(async () => {
    if (!currentThread) {
      throw new Error('No active thread');
    }
    return await resumeConversation(currentThread, 'approve');
  }, [currentThread, resumeConversation]);

  const rejectBooking = useCallback(async () => {
    if (!currentThread) {
      throw new Error('No active thread');
    }
    return await resumeConversation(currentThread, 'reject');
  }, [currentThread, resumeConversation]);

  const modifyBooking = useCallback(async (modification) => {
    if (!currentThread) {
      throw new Error('No active thread');
    }
    return await resumeConversation(currentThread, modification);
  }, [currentThread, resumeConversation]);

  const approvePayment = useCallback(async () => {
    if (!currentThread) {
      throw new Error('No active thread');
    }
    return await resumeConversation(currentThread, 'approve');
  }, [currentThread, resumeConversation]);

  const rejectPayment = useCallback(async () => {
    if (!currentThread) {
      throw new Error('No active thread');
    }
    return await resumeConversation(currentThread, 'reject');
  }, [currentThread, resumeConversation]);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setCurrentThread(null);
    setInterruptData(null);
    setConversationHistory([]);
    setError(null);
    setIsLoading(false);
  }, []);

  return {
    // State
    isLoading,
    error,
    currentThread,
    interruptData,
    conversationHistory,
    
    // Actions
    startConversation,
    resumeConversation,
    getStatus,
    deleteThread,
    clearError,
    cleanup,
    
    // Helper functions
    approveBooking,
    rejectBooking,
    modifyBooking,
    approvePayment,
    rejectPayment,
    
    // Status checks
    isInterrupted: !!interruptData,
    hasActiveThread: !!currentThread,
  };
};
