# Update Telegram Bot untuk HITL (Human-in-the-Loop)

## 🎯 Overview

Telegram bot telah diupdate untuk mendukung HITL (Human-in-the-Loop) functionality, memungkinkan user untuk mereview dan menyetujui booking/payment sebelum diproses.

## 🔧 Fitur Baru yang Ditambahkan

### 1. **HITL State Management**
```python
# Dictionary untuk menyimpan HITL state per user
user_hitl_state = {}
```

### 2. **HITL API Integration**
```python
async def make_hitl_api_call(endpoint: str, method: str = "POST", data: dict = None) -> dict:
    """Memanggil HITL API endpoints."""
```

### 3. **Interrupt Message Formatting**
```python
def format_hitl_interrupt_message(interrupt_data: dict) -> str:
    """Format interrupt data menjadi pesan Telegram yang user-friendly."""
```

### 4. **Inline Keyboard untuk Approval**
```python
def create_hitl_keyboard(interrupt_type: str, thread_id: str) -> InlineKeyboardMarkup:
    """Membuat inline keyboard untuk HITL approval."""
```

## 📱 User Experience Flow

### 1. **Normal Booking Request**
```
User: "Book hotel in Bali for 2 nights"
Bot: "⏳ Mencari hotel..."
```

### 2. **HITL Interrupt**
```
🏨 *Konfirmasi Booking Hotel*

Konfirmasi booking hotel berikut:

📋 *Detail Booking:*
• Hotel Name: Grand Bali Resort
• Check In: 2025-01-15
• Check Out: 2025-01-17
• Guests: 2
• Room Type: Deluxe
• Total Price: Rp 1,500,000

❓ Apakah Anda ingin melanjutkan booking ini?

[✅ Setuju] [❌ Tolak] [✏️ Modifikasi]
```

### 3. **User Actions**

#### **Approve (✅ Setuju)**
```
✅ *Disetujui*

Permintaan Anda telah disetujui dan sedang diproses.

🤖 *Hasil:*
Booking hotel berhasil! Kode booking: HTL123456
```

#### **Reject (❌ Tolak)**
```
❌ *Ditolak*

Permintaan Anda telah dibatalkan.

🤖 *Hasil:*
Booking dibatalkan. Mari cari opsi hotel lain.
```

#### **Modify (✏️ Modifikasi)**
```
✏️ *Modifikasi Booking*

Silakan ketik modifikasi yang Anda inginkan.

Contoh:
• "Ubah tanggal check-in ke 25 Juni"
• "Ganti ke kamar deluxe"
• "Tambah 1 malam lagi"

Ketik modifikasi Anda:
```

User types: "Ubah ke kamar suite"
```
⏳ Memproses modifikasi Anda...

🤖 *Hasil:*
Booking dimodifikasi. Kamar suite tersedia dengan harga Rp 2,200,000
```

## 🔄 Technical Implementation

### 1. **Message Handler Update**
```python
async def handle_user_message(chat_id, user_id, message_text, message_id=None):
    # Cek apakah user sedang dalam proses HITL modification
    hitl_state = user_hitl_state.get(user_id)
    if hitl_state and hitl_state.get("status") == "waiting_modification":
        await handle_hitl_modification_input(chat_id, user_id, message_text, hitl_state)
        return
    
    # Memanggil HITL API untuk start conversation
    hitl_response = await make_hitl_api_call("start", "POST", {
        "message": message_text,
        "user_context": {"user_id": str(user_id), "platform": "telegram"},
        "thread_id": thread_id
    })
    
    # Cek apakah ada interrupt
    if hitl_response.get("status") == "interrupted":
        interrupt_data = hitl_response.get("interrupt_data")
        if interrupt_data:
            await handle_hitl_interrupt(chat_id, user_id, interrupt_data, thread_id)
            return
```

### 2. **Callback Handler**
```python
async def handle_hitl_callback(callback_query):
    """Menangani callback dari tombol HITL approval."""
    callback_data = callback_query.data
    
    # Parse callback data: hitl_approve_thread_id, hitl_reject_thread_id, hitl_modify_thread_id
    parts = callback_data.split("_")
    action = parts[1]  # approve, reject, modify
    thread_id = "_".join(parts[2:])
    
    if action == "approve":
        await process_hitl_approval(chat_id, user_id, thread_id, "approve", message_id)
    elif action == "reject":
        await process_hitl_approval(chat_id, user_id, thread_id, "reject", message_id)
    elif action == "modify":
        await handle_hitl_modification_request(chat_id, user_id, thread_id, message_id)
```

### 3. **Webhook Handler Update**
```python
# Handle callback untuk HITL approval
elif callback_data.startswith("hitl_"):
    await handle_hitl_callback(callback_query)
```

## 🎨 UI Components

### 1. **Interrupt Types dengan Emoji**
- 🏨 Hotel Booking Approval
- ✈️ Flight Booking Approval  
- 🏝️ Tour Booking Approval
- 💳 Payment Approval

### 2. **Keyboard Layouts**

#### **Booking Approval**
```
[✅ Setuju] [❌ Tolak]
[✏️ Modifikasi]
```

#### **Payment Approval**
```
[✅ Setuju] [❌ Tolak]
```

### 3. **Status Messages**
- ⏳ Memproses keputusan Anda...
- ✅ Disetujui - Permintaan diproses
- ❌ Ditolak - Permintaan dibatalkan
- ✏️ Modifikasi - Menunggu input

## 🔧 Configuration

### 1. **Environment Variables**
Tidak ada environment variable baru yang diperlukan. Bot menggunakan API_URL yang sudah ada.

### 2. **Dependencies**
Tidak ada dependency baru yang diperlukan. Menggunakan library yang sudah ada:
- `telegram`
- `httpx`
- `asyncio`

## 🧪 Testing

### 1. **Manual Testing Flow**
1. Kirim pesan booking: "Book hotel in Bali"
2. Tunggu interrupt message dengan keyboard
3. Test semua tombol: Approve, Reject, Modify
4. Test modification input
5. Verify final responses

### 2. **Test Cases**
```python
# Test 1: Hotel booking approval
"Book hotel in Jakarta for 1 night"

# Test 2: Flight booking approval  
"Book flight from Jakarta to Bali"

# Test 3: Payment approval
"Pay my hotel booking HTL123"

# Test 4: Modification flow
"Book hotel in Bali" → Modify → "Change to suite room"
```

## 🚀 Deployment

### 1. **No Breaking Changes**
- Backward compatible dengan existing functionality
- Fallback ke API lama jika HITL gagal
- Existing commands tetap berfungsi

### 2. **Deployment Steps**
1. Deploy backend HITL API endpoints
2. Deploy updated Telegram bot
3. Test HITL functionality
4. Monitor logs untuk errors

## 📊 Monitoring

### 1. **Metrics to Track**
- HITL interrupt frequency
- Approval/rejection rates
- Modification request frequency
- Response times
- Error rates

### 2. **Logging**
```python
logger.info(f"HITL interrupt sent to user {user_id} for thread {thread_id}")
logger.info(f"HITL {decision} processed for user {user_id}, thread {thread_id}")
logger.error(f"Error handling HITL callback: {e}")
```

## ⚠️ Important Notes

### 1. **State Management**
- HITL state disimpan dalam memory (`user_hitl_state`)
- State akan hilang jika bot restart
- Consider menggunakan Redis untuk production

### 2. **Error Handling**
- Fallback ke API lama jika HITL API gagal
- Graceful error messages untuk user
- Comprehensive logging untuk debugging

### 3. **Security**
- Validate callback data untuk prevent injection
- Check user permissions sebelum process approval
- Rate limiting untuk prevent spam

## 🔄 Future Enhancements

1. **Persistent State**: Gunakan Redis/Database untuk HITL state
2. **Timeout Handling**: Auto-expire HITL requests setelah timeout
3. **Batch Approvals**: Support untuk multiple bookings
4. **Admin Override**: Admin dapat approve/reject untuk users
5. **Audit Trail**: Log semua HITL decisions untuk compliance
