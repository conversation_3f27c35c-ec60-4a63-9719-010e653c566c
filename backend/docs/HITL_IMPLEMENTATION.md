# Human-in-the-Loop (HITL) Implementation Guide

## 🎯 Overview

Human-in-the-Loop (HITL) adalah fitur LangGraph yang memungkinkan eksekusi graph dihentikan sementara untuk mendapatkan input atau persetujuan dari manusia sebelum melanjutkan proses.

## 🔧 Core Components

### 1. `interrupt()` Function
```python
from langgraph.types import interrupt

def approval_node(state: State):
    approval = interrupt({
        "type": "booking_approval",
        "message": "Konfirmasi booking hotel",
        "booking_details": state["booking_details"]
    })
    
    if approval == "approve":
        return state
    else:
        return {"status": "cancelled"}
```

### 2. `Command` Primitive
```python
from langgraph.types import Command

# Resume dengan value
graph.invoke(Command(resume="approve"), config)

# Resume dengan state update
graph.invoke(Command(resume="approve", update={"status": "confirmed"}), config)
```

## 🏗️ Architecture

### Graph Structure
```
User Input → Agent → Tool Call → HITL Node → Tool Execution → Response
                        ↓
                   interrupt() 
                        ↓
                  Wait for Human
                        ↓
                Command(resume=value)
```

### HITL Nodes in Our Implementation

1. **hotel_booking_approval_node**: Approval untuk booking hotel
2. **flight_booking_approval_node**: Approval untuk booking flight  
3. **tour_booking_approval_node**: Approval untuk booking tour
4. **payment_approval_node**: Approval untuk pembayaran

## 📋 Implementation Details

### 1. HITL Node Structure
```python
def hotel_booking_approval_node(state: State):
    messages = state.get("messages", [])
    last_message = messages[-1] if messages else None
    
    # Check for booking tool calls
    if (hasattr(last_message, 'tool_calls') and last_message.tool_calls and 
        any('book_hotel' in str(tool_call) for tool_call in last_message.tool_calls)):
        
        # Extract booking details
        booking_details = {}
        for tool_call in last_message.tool_calls:
            if 'book_hotel' in str(tool_call):
                booking_details = tool_call.get('args', {})
                break
        
        # Interrupt for approval
        approval = interrupt({
            "type": "hotel_booking_approval",
            "message": "Konfirmasi booking hotel berikut:",
            "booking_details": booking_details,
            "question": "Apakah Anda ingin melanjutkan booking ini?"
        })
        
        # Handle approval response
        if approval == "approve":
            return state
        elif approval == "reject":
            return {
                "messages": state["messages"] + [
                    ("assistant", "Booking dibatalkan. Mari cari opsi lain.")
                ]
            }
        else:  # modification
            return {
                "messages": state["messages"] + [
                    ("user", f"Mohon modifikasi booking: {approval}")
                ]
            }
    
    return state
```

### 2. Routing Logic
```python
class RouteUpdater:
    async def route_tool_execution(self, state: State):
        # ... existing logic ...
        
        # Check if any tool call requires HITL approval
        booking_tools = ['book_hotel_room', 'book_flight', 'book_tour']
        payment_tools = ['process_hotel_payment', 'process_flight_payment', 'process_tour_payment']
        
        for tc in tool_calls:
            tool_name = tc.get("name", "")
            if tool_name in booking_tools:
                if 'hotel' in tool_name:
                    return "hotel_booking_approval"
                elif 'flight' in tool_name:
                    return "flight_booking_approval"
                elif 'tour' in tool_name:
                    return "tour_booking_approval"
            elif tool_name in payment_tools:
                return "payment_approval"
        
        return self.update_tool
```

### 3. Graph Configuration
```python
def create_graph_builder():
    builder = StateGraph(State)
    
    # Add HITL nodes
    builder.add_node("hotel_booking_approval", hotel_booking_approval_node)
    builder.add_node("flight_booking_approval", flight_booking_approval_node)
    builder.add_node("tour_booking_approval", tour_booking_approval_node)
    builder.add_node("payment_approval", payment_approval_node)
    
    # Configure routing with HITL
    builder.add_conditional_edges(
        "hotel_agent",
        RouteUpdater(hotel_tools, "hotel_agent_tools").route_tool_execution,
        ["hotel_agent_tools", "hotel_booking_approval", "payment_approval", "return_to_supervisor", END],
    )
    
    # Connect approval nodes to tool execution
    builder.add_edge("hotel_booking_approval", "hotel_agent_tools")
    
    return builder
```

## 🌐 API Integration

### 1. Start HITL Conversation
```http
POST /api/v1/hitl/start
{
    "message": "Saya ingin booking hotel di Bali",
    "user_context": {"user_id": "user123"},
    "thread_id": "optional-thread-id"
}
```

Response:
```json
{
    "thread_id": "uuid-thread-id",
    "status": "interrupted",
    "interrupt_data": {
        "type": "hotel_booking_approval",
        "message": "Konfirmasi booking hotel berikut:",
        "booking_details": {...},
        "question": "Apakah Anda ingin melanjutkan booking ini?"
    }
}
```

### 2. Resume Conversation
```http
POST /api/v1/hitl/resume
{
    "thread_id": "uuid-thread-id",
    "resume_value": "approve"
}
```

### 3. Get Status
```http
GET /api/v1/hitl/status/{thread_id}
```

## 🎨 Frontend Integration

### 1. React Hook Usage
```jsx
import { useHITL } from '@/hooks/useHITL';

function BookingChat() {
    const {
        startConversation,
        approveBooking,
        rejectBooking,
        modifyBooking,
        interruptData,
        isInterrupted,
        isLoading
    } = useHITL();
    
    const handleSendMessage = async (message) => {
        await startConversation(message, { user_id: 'user123' });
    };
    
    return (
        <div>
            {isInterrupted && (
                <HITLApproval
                    interruptData={interruptData}
                    onApprove={approveBooking}
                    onReject={rejectBooking}
                    onModify={modifyBooking}
                    isLoading={isLoading}
                />
            )}
        </div>
    );
}
```

### 2. HITL Approval Component
```jsx
<HITLApproval
    interruptData={{
        type: "hotel_booking_approval",
        message: "Konfirmasi booking hotel",
        booking_details: {...},
        question: "Lanjutkan booking?"
    }}
    onApprove={() => approveBooking()}
    onReject={() => rejectBooking()}
    onModify={(text) => modifyBooking(text)}
    isLoading={false}
/>
```

## 🔄 Workflow Examples

### 1. Hotel Booking with Approval
```
1. User: "Book hotel in Bali for 2 nights"
2. Agent: Searches hotels → Finds options → Calls book_hotel_room
3. HITL: Interrupts with booking details for approval
4. User: Reviews and approves
5. System: Executes booking → Confirms to user
```

### 2. Payment Approval
```
1. User: "Pay my hotel booking"
2. Agent: Finds unpaid booking → Calls process_payment
3. HITL: Interrupts with payment details for approval
4. User: Reviews amount and approves
5. System: Processes payment → Sends confirmation
```

## ⚠️ Important Considerations

### 1. Node Re-execution
- Ketika resume dari interrupt, seluruh node dijalankan ulang
- Hindari side effects sebelum interrupt()
- Gunakan state untuk menyimpan data yang sudah diproses

### 2. Error Handling
```python
def safe_hitl_node(state: State):
    try:
        # HITL logic here
        approval = interrupt(data)
        return handle_approval(approval, state)
    except Exception as e:
        logger.error(f"HITL error: {e}")
        return {"error": str(e)}
```

### 3. Timeout Handling
- Implement timeout untuk interrupt yang terlalu lama
- Provide fallback behavior untuk timeout cases

## 🧪 Testing

### 1. Unit Tests
```python
async def test_hotel_booking_approval():
    state = {"messages": [create_booking_message()]}
    result = hotel_booking_approval_node(state)
    assert "__interrupt__" in result
```

### 2. Integration Tests
```python
async def test_full_hitl_workflow():
    graph = await build_graph()
    config = {"configurable": {"thread_id": "test"}}
    
    # Start conversation
    result = await graph.ainvoke({"messages": [("user", "book hotel")]}, config)
    assert "__interrupt__" in result
    
    # Resume with approval
    final_result = await graph.ainvoke(Command(resume="approve"), config)
    assert "booking confirmed" in str(final_result)
```

## 📊 Monitoring & Logging

### 1. Interrupt Metrics
- Track interrupt frequency by type
- Monitor approval/rejection rates
- Measure time to resolution

### 2. Logging
```python
logger.info(f"HITL interrupt triggered: {interrupt_type}")
logger.info(f"User decision: {approval_decision}")
logger.info(f"Resolution time: {resolution_time}s")
```

## 🚀 Best Practices

1. **Clear Interrupt Messages**: Provide clear, actionable information
2. **Structured Data**: Use consistent data structures for interrupt payloads
3. **Timeout Handling**: Implement reasonable timeouts
4. **Error Recovery**: Provide fallback paths for errors
5. **User Experience**: Make approval process intuitive and fast
6. **Security**: Validate all user inputs before resuming
7. **Monitoring**: Track HITL performance and user behavior
