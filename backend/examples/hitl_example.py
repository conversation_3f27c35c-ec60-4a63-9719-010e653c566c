"""
Contoh implementasi Human-in-the-Loop (HITL) dengan LangGraph
Menunjukkan cara menggunakan interrupt() dan Command untuk approval workflow
"""

import asyncio
import uuid
from agents.graph import build_graph
from langgraph.types import Command
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def hitl_booking_example():
    """
    Contoh penggunaan HITL untuk booking hotel dengan approval
    """
    # Build graph dengan HITL nodes
    graph = await build_graph()
    
    # Thread ID untuk conversation
    thread_id = str(uuid.uuid4())
    config = {"configurable": {"thread_id": thread_id}}
    
    print("🤖 Memulai contoh HITL booking...")
    
    # 1. User meminta booking hotel
    user_message = "Saya ingin booking hotel di Bali untuk 2 malam, tanggal 15-17 Januari 2025"
    
    print(f"👤 User: {user_message}")
    
    # Jalankan graph sampai interrupt
    result = await graph.ainvoke({
        "messages": [("user", user_message)],
        "user_context": {"user_id": "user123", "preferences": {"budget": "mid-range"}}
    }, config)
    
    # Cek apakah ada interrupt
    if "__interrupt__" in result:
        interrupt_data = result["__interrupt__"][0]
        print(f"\n⏸️  INTERRUPT: {interrupt_data.value}")
        
        # Simulasi human approval
        if interrupt_data.value.get("type") == "hotel_booking_approval":
            booking_details = interrupt_data.value.get("booking_details", {})
            print(f"\n📋 Detail Booking untuk Review:")
            for key, value in booking_details.items():
                print(f"   {key}: {value}")
            
            # Simulasi user approval
            user_decision = input("\n❓ Apakah Anda menyetujui booking ini? (approve/reject/modify): ").strip().lower()
            
            # Resume dengan decision
            if user_decision == "approve":
                print("✅ Booking disetujui, melanjutkan proses...")
                final_result = await graph.ainvoke(Command(resume="approve"), config)
            elif user_decision == "reject":
                print("❌ Booking ditolak, mencari alternatif...")
                final_result = await graph.ainvoke(Command(resume="reject"), config)
            else:
                modification = input("📝 Masukkan modifikasi yang diinginkan: ")
                print(f"🔄 Memodifikasi booking: {modification}")
                final_result = await graph.ainvoke(Command(resume=modification), config)
            
            print(f"\n🏁 Hasil Akhir: {final_result.get('messages', [])[-1].content if final_result.get('messages') else 'Tidak ada pesan'}")
    
    else:
        print(f"🏁 Hasil: {result.get('messages', [])[-1].content if result.get('messages') else 'Tidak ada pesan'}")

async def hitl_payment_example():
    """
    Contoh penggunaan HITL untuk approval pembayaran
    """
    graph = await build_graph()
    thread_id = str(uuid.uuid4())
    config = {"configurable": {"thread_id": thread_id}}
    
    print("\n💳 Memulai contoh HITL payment...")
    
    # Simulasi state dengan booking yang perlu dibayar
    user_message = "Saya ingin bayar booking hotel saya dengan ID booking123"
    
    print(f"👤 User: {user_message}")
    
    result = await graph.ainvoke({
        "messages": [("user", user_message)],
        "user_context": {"user_id": "user123", "booking_id": "booking123"}
    }, config)
    
    if "__interrupt__" in result:
        interrupt_data = result["__interrupt__"][0]
        print(f"\n⏸️  PAYMENT INTERRUPT: {interrupt_data.value}")
        
        if interrupt_data.value.get("type") == "payment_approval":
            payment_details = interrupt_data.value.get("payment_details", {})
            print(f"\n💰 Detail Pembayaran untuk Review:")
            for key, value in payment_details.items():
                print(f"   {key}: {value}")
            
            user_decision = input("\n❓ Konfirmasi pembayaran? (approve/reject): ").strip().lower()
            
            if user_decision == "approve":
                print("✅ Pembayaran disetujui, memproses...")
                final_result = await graph.ainvoke(Command(resume="approve"), config)
            else:
                print("❌ Pembayaran dibatalkan")
                final_result = await graph.ainvoke(Command(resume="reject"), config)
            
            print(f"\n🏁 Hasil Pembayaran: {final_result.get('messages', [])[-1].content if final_result.get('messages') else 'Tidak ada pesan'}")

async def hitl_validation_example():
    """
    Contoh penggunaan HITL untuk validasi input dengan multiple interrupts
    """
    from langgraph.types import interrupt
    from agents.state import State
    
    def validate_user_input_node(state: State):
        """Node dengan validasi input menggunakan multiple interrupts"""
        question = "Berapa jumlah tamu untuk booking hotel?"
        
        while True:
            answer = interrupt({
                "type": "input_validation",
                "question": question,
                "validation_rule": "Harus berupa angka positif (1-10)"
            })
            
            # Validasi input
            try:
                guest_count = int(answer)
                if 1 <= guest_count <= 10:
                    break  # Input valid
                else:
                    question = f"'{answer}' tidak valid. Jumlah tamu harus antara 1-10. Berapa jumlah tamu?"
            except ValueError:
                question = f"'{answer}' bukan angka valid. Berapa jumlah tamu (1-10)?"
        
        return {"guest_count": guest_count}
    
    print("\n🔍 Contoh validasi input dengan HITL:")
    print("Node ini akan terus meminta input sampai valid (1-10)")

async def main():
    """
    Menjalankan semua contoh HITL
    """
    try:
        print("🚀 Memulai contoh-contoh HITL LangGraph...")
        
        # Contoh 1: Booking approval
        await hitl_booking_example()
        
        # Contoh 2: Payment approval  
        await hitl_payment_example()
        
        # Contoh 3: Input validation
        await hitl_validation_example()
        
        print("\n✨ Semua contoh HITL selesai!")
        
    except Exception as e:
        logger.error(f"Error dalam contoh HITL: {e}")
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
