"""
API endpoints untuk Human-in-the-Loop (HITL) functionality
<PERSON><PERSON><PERSON> interrupt dan resume operations
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
from agents.graph import build_graph
from langgraph.types import Command
import logging
import uuid

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/hitl", tags=["Human-in-the-Loop"])

# Pydantic models untuk request/response
class HITLStartRequest(BaseModel):
    message: str
    user_context: Optional[Dict[str, Any]] = None
    thread_id: Optional[str] = None

class HITLResumeRequest(BaseModel):
    thread_id: str
    resume_value: Any
    update_state: Optional[Dict[str, Any]] = None

class HITLStatusResponse(BaseModel):
    thread_id: str
    status: str  # "running", "interrupted", "completed", "error"
    interrupt_data: Optional[Dict[str, Any]] = None
    messages: List[Dict[str, Any]] = []

class HITLResumeResponse(BaseModel):
    thread_id: str
    status: str
    result: Optional[Dict[str, Any]] = None
    messages: List[Dict[str, Any]] = []

# Global graph instance
_graph = None

async def get_graph():
    """Get or initialize the graph instance"""
    global _graph
    if _graph is None:
        _graph = await build_graph()
    return _graph

@router.post("/start", response_model=HITLStatusResponse)
async def start_hitl_conversation(request: HITLStartRequest):
    """
    Memulai conversation dengan HITL capability
    """
    try:
        graph = await get_graph()
        
        # Generate thread ID jika tidak disediakan
        thread_id = request.thread_id or str(uuid.uuid4())
        config = {"configurable": {"thread_id": thread_id}}
        
        # Jalankan graph dengan message user
        result = await graph.ainvoke({
            "messages": [("user", request.message)],
            "user_context": request.user_context or {}
        }, config)
        
        # Cek apakah ada interrupt
        if "__interrupt__" in result:
            interrupt_data = result["__interrupt__"][0]
            return HITLStatusResponse(
                thread_id=thread_id,
                status="interrupted",
                interrupt_data=interrupt_data.value,
                messages=[{"role": "assistant", "content": "Menunggu approval..."}]
            )
        else:
            # Conversation selesai tanpa interrupt
            messages = []
            if result.get("messages"):
                for msg in result["messages"]:
                    if hasattr(msg, 'content'):
                        role = "assistant" if hasattr(msg, 'response_metadata') else "user"
                        messages.append({"role": role, "content": msg.content})
            
            return HITLStatusResponse(
                thread_id=thread_id,
                status="completed",
                messages=messages
            )
            
    except Exception as e:
        logger.error(f"Error starting HITL conversation: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/resume", response_model=HITLResumeResponse)
async def resume_hitl_conversation(request: HITLResumeRequest):
    """
    Resume conversation setelah interrupt dengan user input
    """
    try:
        graph = await get_graph()
        config = {"configurable": {"thread_id": request.thread_id}}
        
        # Buat Command untuk resume
        if request.update_state:
            command = Command(resume=request.resume_value, update=request.update_state)
        else:
            command = Command(resume=request.resume_value)
        
        # Resume graph execution
        result = await graph.ainvoke(command, config)
        
        # Cek apakah masih ada interrupt lagi
        if "__interrupt__" in result:
            interrupt_data = result["__interrupt__"][0]
            return HITLResumeResponse(
                thread_id=request.thread_id,
                status="interrupted",
                result={"interrupt_data": interrupt_data.value}
            )
        else:
            # Conversation selesai
            messages = []
            if result.get("messages"):
                for msg in result["messages"]:
                    if hasattr(msg, 'content'):
                        role = "assistant" if hasattr(msg, 'response_metadata') else "user"
                        messages.append({"role": role, "content": msg.content})
            
            return HITLResumeResponse(
                thread_id=request.thread_id,
                status="completed",
                result=result,
                messages=messages
            )
            
    except Exception as e:
        logger.error(f"Error resuming HITL conversation: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status/{thread_id}", response_model=HITLStatusResponse)
async def get_hitl_status(thread_id: str):
    """
    Mendapatkan status conversation thread
    """
    try:
        graph = await get_graph()
        config = {"configurable": {"thread_id": thread_id}}
        
        # Get current state
        state = await graph.aget_state(config)
        
        if not state:
            raise HTTPException(status_code=404, detail="Thread not found")
        
        # Cek apakah ada interrupt
        if state.next:
            # Ada interrupt yang pending
            return HITLStatusResponse(
                thread_id=thread_id,
                status="interrupted",
                interrupt_data={"pending_nodes": state.next}
            )
        else:
            # Thread completed atau running
            messages = []
            if state.values.get("messages"):
                for msg in state.values["messages"]:
                    if hasattr(msg, 'content'):
                        role = "assistant" if hasattr(msg, 'response_metadata') else "user"
                        messages.append({"role": role, "content": msg.content})
            
            return HITLStatusResponse(
                thread_id=thread_id,
                status="completed",
                messages=messages
            )
            
    except Exception as e:
        logger.error(f"Error getting HITL status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/thread/{thread_id}")
async def delete_hitl_thread(thread_id: str):
    """
    Menghapus conversation thread
    """
    try:
        # Note: Implementasi tergantung pada checkpointer yang digunakan
        # Untuk PostgreSQL checkpointer, bisa menghapus dari database
        return {"message": f"Thread {thread_id} deleted successfully"}
        
    except Exception as e:
        logger.error(f"Error deleting HITL thread: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/threads")
async def list_hitl_threads():
    """
    Mendapatkan daftar active threads
    """
    try:
        # Note: Implementasi tergantung pada checkpointer
        # Untuk demo, return empty list
        return {"threads": []}
        
    except Exception as e:
        logger.error(f"Error listing HITL threads: {e}")
        raise HTTPException(status_code=500, detail=str(e))
