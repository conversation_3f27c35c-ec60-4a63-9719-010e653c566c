"""
Script untuk debug HITL implementation
Test langsung tanpa melalui Telegram bot
"""

import asyncio
import uuid
import logging
from agents.graph import build_graph
from langgraph.types import Command

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_hitl_booking():
    """
    Test HITL untuk booking hotel
    """
    try:
        print("🚀 Building graph...")
        graph = await build_graph()
        
        # Thread ID untuk conversation
        thread_id = str(uuid.uuid4())
        config = {"configurable": {"thread_id": thread_id}}
        
        print(f"📱 Thread ID: {thread_id}")
        
        # 1. User meminta booking hotel
        user_message = "Bisa pesan kamar jungle view room di jungle retreat ubud untuk tanggal 27 juni sampai 29 juni 2025, untuk 2 orang, 1 kamar"
        
        print(f"👤 User: {user_message}")
        
        # Jalankan graph sampai interrupt
        print("⏳ Running graph...")
        result = await graph.ainvoke({
            "messages": [("user", user_message)],
            "user_context": {
                "user_id": "408619853", 
                "platform": "telegram",
                "nama_pemesan": "Test User",
                "email": "<EMAIL>", 
                "telepon": "081234567890"
            }
        }, config)
        
        print(f"📊 Result keys: {list(result.keys())}")
        
        # Cek apakah ada interrupt
        if "__interrupt__" in result:
            interrupt_data = result["__interrupt__"][0]
            print(f"\n⏸️  INTERRUPT DETECTED!")
            print(f"📋 Interrupt data: {interrupt_data.value}")
            
            # Simulasi user approval
            print(f"\n❓ Simulating user approval...")
            
            # Resume dengan approval
            print("✅ Approving booking...")
            final_result = await graph.ainvoke(Command(resume="approve"), config)
            
            print(f"🏁 Final result keys: {list(final_result.keys())}")
            if final_result.get('messages'):
                print(f"💬 Final messages: {len(final_result['messages'])} messages")
                for i, msg in enumerate(final_result['messages'][-3:]):  # Show last 3 messages
                    print(f"   {i}: {type(msg).__name__} - {getattr(msg, 'content', str(msg))[:100]}...")
        
        else:
            print(f"🏁 No interrupt - Result: {result}")
            if result.get('messages'):
                print(f"💬 Messages: {len(result['messages'])} messages")
                for i, msg in enumerate(result['messages'][-3:]):  # Show last 3 messages
                    print(f"   {i}: {type(msg).__name__} - {getattr(msg, 'content', str(msg))[:100]}...")
    
    except Exception as e:
        logger.error(f"❌ Error in HITL test: {e}")
        import traceback
        traceback.print_exc()

async def test_simple_message():
    """
    Test simple message tanpa booking untuk comparison
    """
    try:
        print("\n🔍 Testing simple message...")
        graph = await build_graph()
        
        thread_id = str(uuid.uuid4())
        config = {"configurable": {"thread_id": thread_id}}
        
        user_message = "Halo, bisa carikan hotel di ubud?"
        print(f"👤 User: {user_message}")
        
        result = await graph.ainvoke({
            "messages": [("user", user_message)],
            "user_context": {
                "user_id": "408619853", 
                "platform": "telegram"
            }
        }, config)
        
        print(f"📊 Simple result keys: {list(result.keys())}")
        if result.get('messages'):
            print(f"💬 Messages: {len(result['messages'])} messages")
            for i, msg in enumerate(result['messages'][-2:]):  # Show last 2 messages
                print(f"   {i}: {type(msg).__name__} - {getattr(msg, 'content', str(msg))[:100]}...")
    
    except Exception as e:
        logger.error(f"❌ Error in simple test: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """
    Run all tests
    """
    print("🧪 Starting HITL Debug Tests...")
    
    # Test 1: Simple message
    await test_simple_message()
    
    # Test 2: HITL booking
    await test_hitl_booking()
    
    print("\n✨ Tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
